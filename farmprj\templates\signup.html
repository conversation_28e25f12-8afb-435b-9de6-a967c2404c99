{% extends 'base.html' %}

{% block title %}
    {% if user_type == 'farmer' %}
        Farmer Signup
    {% else %}
        User Signup
    {% endif %}
{% endblock %}

{% block content %}
<div class="form-container">
    <h2 class="text-center mb-4">
        {% if user_type == 'farmer' %}
            Sign Up as Farmer
        {% else %}
            Sign Up as User
        {% endif %}
    </h2>

    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        {% for field in form %}
            <div class="mb-3">
                <label for="{{ field.id_for_label }}" class="form-label">
                    {{ field.label }}
                </label>
                {% if field.field.widget.input_type == 'file' %}
                    <input type="{{ field.field.widget.input_type }}" 
                           name="{{ field.html_name }}"
                           class="form-control" 
                           id="{{ field.id_for_label }}">
                {% else %}
                    {{ field }}
                {% endif %}
                {% if field.help_text %}
                    <small class="form-text text-muted">{{ field.help_text }}</small>
                {% endif %}
                {% if field.errors %}
                    {% for error in field.errors %}
                        <div class="alert alert-danger">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        {% endfor %}

        <div class="d-grid gap-2">
            <button type="submit" class="btn btn-success">Sign Up</button>
        </div>
    </form>

    <div class="text-center mt-3">
        <p>Already have an account? <a href="{% url 'login' %}">Login here</a></p>
    </div>
</div>
{% endblock %} 