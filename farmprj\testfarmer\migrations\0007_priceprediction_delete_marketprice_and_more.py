# Generated by Django 5.1.6 on 2025-02-28 06:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testfarmer', '0006_marketprice'),
    ]

    operations = [
        migrations.CreateModel(
            name='PricePrediction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commodity', models.CharField(max_length=100)),
                ('district', models.CharField(max_length=100)),
                ('predicted_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('prediction_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.DeleteModel(
            name='MarketPrice',
        ),
        migrations.AddIndex(
            model_name='priceprediction',
            index=models.Index(fields=['commodity', 'district', 'prediction_date'], name='testfarmer__commodi_562be8_idx'),
        ),
    ]
