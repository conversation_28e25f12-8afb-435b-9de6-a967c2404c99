# Generated by Django 5.1.6 on 2025-02-09 05:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testfarmer', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Rating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)])),
                ('comment', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('farmer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='testfarmer.farmerprofile')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='testfarmer.userprofile')),
            ],
            options={
                'unique_together': {('farmer', 'user')},
            },
        ),
    ]
