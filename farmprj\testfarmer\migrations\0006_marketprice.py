# Generated by Django 5.1.6 on 2025-02-28 05:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testfarmer', '0005_alter_product_quantity'),
    ]

    operations = [
        migrations.CreateModel(
            name='MarketPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('state', models.CharField(max_length=100)),
                ('district', models.CharField(max_length=100)),
                ('market', models.CharField(max_length=100)),
                ('commodity', models.Char<PERSON>ield(max_length=100)),
                ('variety', models.Char<PERSON>ield(max_length=100)),
                ('min_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('max_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('modal_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'indexes': [models.Index(fields=['commodity', 'district', 'price_date'], name='testfarmer__commodi_384930_idx'), models.Index(fields=['state', 'commodity'], name='testfarmer__state_c3df5b_idx')],
            },
        ),
    ]
