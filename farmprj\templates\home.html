{% extends 'base.html' %}

{% block title %}Browse Farmers{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2 class="text-center">Our Farmers</h2>
        </div>
    </div>
    <div id="google_translate_element"></div>
<script type="text/javascript">
  function googleTranslateElementInit() {
    new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
  }
</script>
<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>


    <div class="row">
        {% for farmer in farmers %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="product-img-container">
                        {% if farmer.farm_image %}
                            <img src="{{ farmer.farm_image.url }}"
                                 class="card-img-top product-img"
                                 alt="{{ farmer.farm_name }}"
                                 data-full-img="{{ farmer.farm_image.url }}"
                                 onclick="openImageModal('{{ farmer.farm_image.url }}')">
                        {% else %}
                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                <i class="fas fa-tractor fa-3x text-success"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">
                            {{ farmer.farm_name }}
                            <div class="d-flex align-items-center mt-1">
                                <div class="star-rating small">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= farmer.average_rating %}
                                            <i class="fas fa-star text-warning"></i>
                                        {% elif forloop.counter <= farmer.average_rating|add:0.5 %}
                                            <i class="fas fa-star-half-alt text-warning"></i>
                                        {% else %}
                                            <i class="far fa-star text-warning"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <small class="text-muted ms-2">({{ farmer.ratings.count }})</small>
                            </div>
                        </h5>
                        <p class="card-text">{{ farmer.farm_description|truncatewords:30 }}</p>
                        <p class="card-text">
                            <i class="fas fa-map-marker-alt text-success"></i> {{ farmer.farm_location }}
                            {% if farmer.has_distance %}
                                <br>
                                <span class="badge bg-success mt-1">
                                    <i class="fas fa-location-arrow"></i> {{ farmer.distance }} km away
                                </span>
                            {% endif %}
                        </p>
                        <a href="{% url 'farmer_profile' farmer.id %}" class="btn btn-success">View Profile</a>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-12 text-center">
                <p>No farmers found {% if radius %}within {{ radius }} km{% endif %}.</p>
            </div>
        {% endfor %}
    </div>
</div>

{% block extra_js %}
{% if user_lat and user_lng %}
<script>
document.addEventListener('DOMContentLoaded', () => {
    const radiusSelect = document.getElementById('radiusSelect');
    if (radiusSelect) {
        radiusSelect.value = '{{ radius }}';
    }
});
</script>
{% endif %}
{% endblock %}
{% endblock %}