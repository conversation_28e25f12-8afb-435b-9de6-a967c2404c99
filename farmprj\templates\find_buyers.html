{% extends 'base.html' %}

{% block title %}Find Buyers{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-4">Buyer Requests</h2>

    <!-- Display total buyers -->
    <div class="alert alert-success">
        <strong>Total Buyers:</strong> {{ total_buyers }}
    </div>

    <!-- Search Form -->
    <form method="GET" action="{% url 'find_buyers' %}" class="mb-4">
        <div class="input-group">
            <input type="text" name="q" class="form-control me-2" placeholder="Search by commodity..." value="{{ request.GET.q }}">
            
            <input type="text" name="district" class="form-control me-2" placeholder="Search by district..." value="{{ request.GET.district }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> Search
            </button>
        </div>
    </form>
  

    {% if buyer_requests %}
    <div class="row">
        {% for request in buyer_requests %}
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-success text-white text-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-seedling me-2"></i> {{ request.product_name }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Buyer Details Section -->
                            <div class="col-8">
                                <h6 class="text-muted mb-2">Buyer Details:</h6>
                                <p class="mb-1"><i class="fas fa-user me-2"></i> {{ request.user.username }}</p>
                                <p class="mb-1"><i class="fas fa-phone-alt me-2"></i> {{ request.user.userprofile.phone }}</p>
                                <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i> {{ request.user.userprofile.city }}, {{ request.district }}</p>
                            </div>
                            <div class="col-4 text-end">
                                <a href="tel:{{ request.user.userprofile.phone }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-phone"></i> Call
                                </a>
                            </div>
                        </div>

                        <!-- Message Section -->
                        <div class="mt-3 p-3 bg-light rounded border">
                            <h6 class="text-muted mb-2"><i class="fas fa-envelope me-2"></i>Message:</h6>
                            <p class="mb-0">{{ request.message }}</p>
                        </div>

                        <!-- Product Info Section -->
                        <div class="row mt-3">
                            <div class="col-6">
                                <h6 class="text-muted mb-1"><i class="fas fa-weight-hanging me-2"></i>Quantity:</h6>
                                <p class="fw-bold mb-0">{{ request.quantity }} kg</p>
                            </div>
                            <div class="col-6 text-end">
                                <h6 class="text-muted mb-1"><i class="fas fa-map-pin me-2"></i>District:</h6>
                                <p class="fw-bold mb-0">{{ request.district }}</p>
                            </div>
                        </div>

                        <!-- Date Section -->
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="far fa-clock me-1"></i> Posted on {{ request.created_at|date:"F d, Y" }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle me-2"></i>No buyer requests found for "{{ query }}".
        </div>
    {% endif %}
</div>
{% endblock %}
