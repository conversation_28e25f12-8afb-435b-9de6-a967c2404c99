# Generated by Django 5.1.6 on 2025-04-05 04:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('testfarmer', '0009_productrequest_district_productrequest_quantity_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='News',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expiry_date', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name_plural': 'News',
                'ordering': ['-created_at'],
            },
        ),
    ]
