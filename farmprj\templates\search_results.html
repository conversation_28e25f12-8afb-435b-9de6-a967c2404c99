{% extends 'base.html' %}

{% block title %}Search Results{% endblock %}

{% block content %}
<div class="container">
    <!-- Search Results Summary -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Search Results {% if query or selected_region %}for {% if query %}"{{ query }}"{% endif %}
            {% if selected_region %}in {{ selected_region }}{% endif %}{% endif %}</h2>
    </div>

    {% if search_type in 'all,farms' %}
    <div class="farms-section mb-5">
        <h3>Farms ({{ results.farms|length }})</h3>
        <div class="row">
            {% for farmer in results.farms %}
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="product-img-container">
                            {% if farmer.farm_image %}
                                <img src="{{ farmer.farm_image.url }}"
                                     class="card-img-top product-img"
                                     alt="{{ farmer.farm_name }}"
                                     data-full-img="{{ farmer.farm_image.url }}"
                                     onclick="openImageModal('{{ farmer.farm_image.url }}')">
                            {% else %}
                                <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                    <i class="fas fa-tractor fa-3x text-success"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">
                                {{ farmer.farm_name }}
                                <div class="d-flex align-items-center mt-1">
                                    <div class="star-rating small">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= farmer.average_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% elif forloop.counter <= farmer.average_rating|add:0.5 %}
                                                <i class="fas fa-star-half-alt text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-warning"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted ms-2">({{ farmer.ratings.count }})</small>
                                </div>
                            </h5>
                            <p class="card-text">{{ farmer.farm_description|truncatewords:30 }}</p>
                            <p class="card-text">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt"></i> {{ farmer.farm_location }}
                                </small>
                            </p>
                            <a href="{% url 'farmer_profile' farmer.id %}" class="btn btn-success">View Profile</a>
                        </div>
                    </div>
                </div>
            {% empty %}
                {% if search_type == 'farms' or search_type == 'all' %}
                    <div class="col-12">
                        <p>No farms found matching your search.</p>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if search_type in 'all,products' %}
    <div class="products-section">
        <h3>Products ({{ results.products|length }})</h3>
        <div class="row">
            {% for product in results.products %}
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="product-img-container">
                            {% if product.image %}
                                <img src="{{ product.image.url }}"
                                     class="card-img-top product-img"
                                     alt="{{ product.name }}"
                                     data-full-img="{{ product.image.url }}"
                                     onclick="openImageModal('{{ product.image.url }}')">
                            {% else %}
                                <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                    <i class="fas fa-seedling fa-3x text-success"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text">{{ product.description|truncatewords:20 }}</p>
                            <p class="card-text">
                                <strong>Price:</strong> Rs. {{ product.price }}<br>
                                <strong>Quantity:</strong> {{ product.quantity }} kg<br>
                                <strong>Farmer:</strong> {{ product.farmer.farm_name }}
                            </p>
                            <a href="{% url 'farmer_profile' product.farmer.id %}" class="btn btn-success">View Farmer</a>
                        </div>
                    </div>
                </div>
            {% empty %}
                {% if search_type == 'products' or search_type == 'all' %}
                    <div class="col-12">
                        <p>No products found matching your search.</p>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if not results.farms and not results.products %}
        <div class="alert alert-info">
            No results found for your search. Try different keywords or search type.
        </div>
    {% endif %}
</div>
{% endblock %}