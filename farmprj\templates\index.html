{% extends 'base.html' %}

{% block title %}Welcome to Farmer's Market{% endblock %}

{% block content %}
<!-- News Ticker -->
{% if news_items %}
<div class="news-ticker bg-success text-white py-2">
    <div class="container">
        <div class="d-flex align-items-center">
            <span class="me-3"><i class="fas fa-newspaper"></i> Latest News:</span>
            <div class="news-scroll position-relative flex-grow-1">
                <div class="news-scroll-content">
                    {% for news in news_items %}
                        <span class="news-item me-4" data-bs-toggle="modal" data-bs-target="#newsModal{{ news.id }}"
                              style="cursor: pointer; transition: all 0.3s ease;"
                              onmouseover="this.style.color='#e9ecef'; this.style.transform='scale(1.05)'"
                              onmouseout="this.style.color='inherit'; this.style.transform='scale(1)'">
                            {{ news.title }}
                        </span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- News Modals -->
{% for news in news_items %}
<div class="modal fade" id="newsModal{{ news.id }}" tabindex="-1" aria-labelledby="newsModalLabel{{ news.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newsModalLabel{{ news.id }}">{{ news.title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {{ news.content|linebreaks }}
                <small class="text-muted d-block mt-3">Posted on: {{ news.created_at|date:"F d, Y" }}</small>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endif %}

<div class="heroo-section text-center py-5 bg-light">
    <h1 class="display-4">Welcome to Farmer's Market</h1>
    <p class="lead">Connect directly with local farmers and get fresh produce</p>
    {% if not user.is_authenticated %}
        <div class="mt-4">
            <a href="{% url 'user_signup' %}" class="btn btn-success btn-lg mx-2">Sign Up as User</a>
            <a href="{% url 'farmer_signup' %}" class="btn btn-outline-success btn-lg mx-2">Sign Up as Farmer</a>
        </div>
    {% endif %}
</div>

<!-- Location Search -->
<div class="container mt-4">
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-end">
                <div class="col-md-4">
                    <label for="location" class="form-label">Search by Farm Location</label>
                    <input type="text" class="form-control" id="location" name="location"
                           placeholder="Enter farm location..."
                           value="{{ selected_location }}">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-success w-100">Search</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Market Summary Cards -->
<div class="container">
    <div class="row">
        <div class="col-md-4">
            <div class="card text-center mb-4">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h5 class="card-title">Active Farmers</h5>
                    <h2 class="card-text">{{ market_summary.total_farmers }}</h2>
                    {% if selected_location %}
                    <small class="text-muted">in {{ selected_location }}</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center mb-4">
                <div class="card-body">
                    <i class="fas fa-shopping-basket fa-2x text-success mb-2"></i>
                    <h5 class="card-title">Available Products</h5>
                    <h2 class="card-text">{{ market_summary.total_products }}</h2>
                    {% if selected_location %}
                    <small class="text-muted">in {{ selected_location }}</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center mb-4">
                <div class="card-body">
                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                    <h5 class="card-title">Active Requests</h5>
                    <h2 class="card-text">{{ market_summary.total_requests|default:"0" }}</h2>
                    {% if selected_location %}
                    <small class="text-muted">in {{ selected_location }}</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Market Insights -->
<div class="container mt-4">
    <div class="row">
        <!-- Trending Products -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-fire"></i> Products in Demand
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for product in trending_products %}
                            <div class="list-group-item">
                                <h6 class="mb-1">{{ product.product_name|default:"Unknown Product" }}</h6>
                                <small class="text-muted">
                                    {{ product.request_count|default:"0" }} requests
                                    {% if product.total_quantity %}
                                        ({{ product.total_quantity|floatformat:0 }} kg)
                                    {% endif %}
                                    <br>

                                </small>
                            </div>
                        {% empty %}
                            <p class="text-muted">No product requests available</p>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Farming Regions -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-map-marker-alt"></i> Top Farming Regions
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for region in top_regions %}
                            <div class="list-group-item">
                                <h6 class="mb-1">{{ region.region }}</h6>
                                <small class="text-muted">
                                    {{ region.product_count }} products ({{ region.total_quantity }} kg available)
                                </small>
                            </div>
                        {% empty %}
                            <p class="text-muted">No regions data available</p>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Farming Communities -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-users"></i> Active Farming Communities
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for region in active_farmers %}
                            <div class="list-group-item">
                                <h6 class="mb-1">{{ region.region }}</h6>
                                <small class="text-muted">
                                    {{ region.farmer_count }} active farmers
                                </small>
                            </div>
                        {% empty %}
                            <p class="text-muted">No farmer data available</p>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Products -->
<div class="container mt-4">
    <h2 class="text-center mb-4">Recent Products</h2>
    <div class="row">
        {% for product in recent_products %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="product-img-container">
                        {% if product.image %}
                            <img src="{{ product.image.url }}"
                                 class="card-img-top product-img"
                                 alt="{{ product.name }}"
                                 data-full-img="{{ product.image.url }}"
                                 onclick="openImageModal('{{ product.image.url }}')">
                        {% else %}
                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                <i class="fas fa-seedling fa-3x text-success"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="card-text">{{ product.description|truncatewords:20 }}</p>
                        <p class="card-text">
                            <strong>Price:</strong> Rs. {{ product.price }}<br>
                            <strong>Quantity:</strong> {{ product.quantity }} kg
                        </p>
                        <p class="card-text">
                            <small class="text-muted">By {{ product.farmer.farm_name }}</small>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{% url 'farmer_profile' product.farmer.id %}" class="btn btn-success">View Details</a>
                            {% if product.farmer.user_profile.phone %}
                                <a href="tel:{{ product.farmer.user_profile.phone }}"
                                   class="btn btn-outline-success">
                                    <i class="fas fa-phone"></i> Call Farmer
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-12 text-center">
                <p>No products available yet.</p>
            </div>
        {% endfor %}
    </div>
</div>

<div class="features-section mt-5 py-5 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4">
                <i class="fas fa-tractor fa-3x mb-3 text-success"></i>
                <h3>Direct from Farmers</h3>
                <p>Connect directly with local farmers</p>
            </div>
            <div class="col-md-4">
                <i class="fas fa-leaf fa-3x mb-3 text-success"></i>
                <h3>Fresh Produce</h3>
                <p>Get fresh and organic products</p>
            </div>
            <div class="col-md-4">
                <i class="fas fa-handshake fa-3x mb-3 text-success"></i>
                <h3>Support Local</h3>
                <p>Support your local farming community</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effect for news items
        const newsItems = document.querySelectorAll('.news-item');
        newsItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Add modal animation
        const newsModals = document.querySelectorAll('.modal');
        newsModals.forEach(modal => {
            modal.addEventListener('show.bs.modal', function() {
                this.style.animation = 'modalFadeIn 0.3s';
            });
        });
    });
</script>
{% endblock %}