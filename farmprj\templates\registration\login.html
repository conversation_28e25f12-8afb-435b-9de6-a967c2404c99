{% extends 'base.html' %}

{% block title %}Login{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="avatar">
                <i class="fas fa-user-circle"></i>
            </div>
            <h3>Welcome Back!</h3>
            <p class="text-muted">Please login to your account</p>
        </div>

        <form method="post" class="login-form">
            {% csrf_token %}
            
            <div class="form-group mb-3">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                    <input type="text" name="username" class="form-control" placeholder="Username" required>
                </div>
            </div>
            
            <div class="form-group mb-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" name="password" class="form-control" placeholder="Password" required>
                </div>
            </div>

            {% if form.errors %}
                <div class="error-message mb-3">
                    <i class="fas fa-exclamation-circle"></i> Invalid username or password
                </div>
            {% endif %}

            <div class="d-grid">
                <button type="submit" class="btn btn-success login-btn">
                    Login
                </button>
            </div>
        </form>

        <div class="login-footer">
            <p class="text-muted">Don't have an account?</p>
            <div class="signup-buttons">
                <a href="{% url 'user_signup' %}" class="btn btn-outline-success">
                    Sign Up as User
                </a>
                <a href="{% url 'farmer_signup' %}" class="btn btn-outline-success">
                    Sign Up as Farmer
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: calc(100vh - 300px);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .login-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .avatar {
        width: 80px;
        height: 80px;
        background: #198754;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
    }

    .avatar i {
        font-size: 40px;
        color: white;
    }

    .login-header h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .login-form .input-group {
        border: 1px solid #ced4da;
        border-radius: 5px;
        overflow: hidden;
    }

    .login-form .input-group:focus-within {
        border-color: #198754;
        box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
    }

    .login-form .input-group-text {
        background: white;
        border: none;
        color: #6c757d;
        padding-left: 1rem;
    }

    .login-form .form-control {
        border: none;
        padding: 0.75rem;
        height: auto;
    }

    .login-form .form-control:focus {
        box-shadow: none;
    }

    .error-message {
        color: #dc3545;
        background: rgba(220, 53, 69, 0.1);
        padding: 0.75rem;
        border-radius: 5px;
        font-size: 0.875rem;
        text-align: center;
    }

    .login-btn {
        padding: 0.75rem;
        font-size: 1rem;
        border-radius: 5px;
    }

    .login-footer {
        text-align: center;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e0e0e0;
    }

    .signup-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }

    .signup-buttons .btn {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    @media (max-width: 576px) {
        .login-card {
            padding: 1.5rem;
        }

        .signup-buttons {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %} 