<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Farmer's Market{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC9zwtLRgX6vCvR8K16AzqlHq70H-VhAfM&libraries=places"></script>
    <style>
        #google_translate_element select {
            background-color: #f8f9fa;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            color: #495057;
            max-width: 200px;
        }
        .goog-te-gadget-simple{
            margin:0rem 1rem;
            border-radius: 7px;
        }
        .navbar-brand{
            text-align: center;
        }
        .navbar-brand span{
            font-size: 2vh;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="{% url 'index' %}">Farmer's <br> <span>Market</span></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <form class="d-flex mx-auto" action="{% url 'search' %}" method="GET">
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               placeholder="Search farms or products..."
                               name="q"
                               value="{{ request.GET.q }}">
                        <select class="form-select" name="type" style="max-width: 80px;">
                            <option value="all" {% if request.GET.type == 'all' %}selected{% endif %}>All</option>
                            <option value="farms" {% if request.GET.type == 'farms' %}selected{% endif %}>Farms</option>
                            <option value="products" {% if request.GET.type == 'products' %}selected{% endif %}>Products</option>
                        </select>
                        <button class="btn btn-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <div id="google_translate_element">

                </div>

                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'market_prices' %}">
                            <i class="fas fa-chart-line"></i> Market Prices
                        </a>
                    </li>

                    {% if user.is_authenticated %}
                        {% if user.userprofile.is_farmer %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'find_buyers' %}">Find_Buyers</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'farmer_dashboard' %}">Dashboard</a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'user_profile' %}">My Profile</a>
                            </li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'home' %}">Browse Farmers</a>
                        </li>
                        {% if user.is_authenticated and not user.userprofile.is_farmer %}
                            <li class="nav-item ms-2">
                                <div class="d-flex align-items-center">
                                    <select class="form-select form-select-sm me-2" id="radiusSelect" style="width: auto;">
                                        <option value="5">5 km</option>
                                        <option value="10" selected>10 km</option>
                                        <option value="20">20 km</option>
                                        <option value="50">50 km</option>
                                    </select>
                                    <button onclick="getNearbyFarms()" class="btn btn-outline-light btn-sm">
                                        <i class="fas fa-location-dot"></i> Find Nearby Farms
                                    </button>
                                </div>
                            </li>
                        {% endif %}
                        <li class="nav-item">
                            <form method="post" action="{% url 'logout' %}" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn nav-link">Logout</button>
                            </form>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'user_signup' %}">User Signup</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'farmer_signup' %}">Farmer Signup</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">Login</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}
        {% endblock %}
    </div>

    <footer class="bg-dark text-white mt-5 py-3">
        <div class="container text-center">
            <p>&copy; 2024 Farmer's Market. All rights reserved.</p>
        </div>
    </footer>

    <!-- Image Modal -->
    <div id="imageModal" class="img-modal">
        <span class="img-modal-close">&times;</span>
        <img class="img-modal-content" id="modalImage">
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Include main JavaScript file -->
    <script src="{% static 'js/main.js' %}"></script>

    {% block extra_js %}{% endblock %}
    <script>
    function getNearbyFarms() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const radius = document.getElementById('radiusSelect').value;
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    window.location.href = `/home/<USER>
                },
                (error) => {
                    alert("Please enable location access to find nearby farms.");
                }
            );
        } else {
            alert("Your browser doesn't support geolocation.");
        }
    }
    </script>
    <script type="text/javascript">
        function googleTranslateElementInit() {
            new google.translate.TranslateElement({
                pageLanguage: 'en',
                includedLanguages: 'en,hi,gu',
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                autoDisplay: false
            }, 'google_translate_element');
        }
    </script>
    <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
</body>
</html>